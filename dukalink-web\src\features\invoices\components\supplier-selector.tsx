"use client";

import React, { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSuppliers, Supplier } from "@/features/suppliers/hooks/use-suppliers";

interface SupplierSelectorProps {
  value?: number;
  onValueChange: (value: number | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function SupplierSelector({
  value,
  onValueChange,
  placeholder = "Select supplier",
  disabled = false,
}: SupplierSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch suppliers with search
  const { data: suppliersData, isLoading } = useSuppliers({
    search: searchTerm || undefined,
  });

  const suppliers = suppliersData?.data || [];

  // Find selected supplier
  const selectedSupplier = useMemo(() => {
    return suppliers.find((supplier) => supplier.id === value);
  }, [suppliers, value]);

  const handleSelect = (supplierId: number) => {
    onValueChange(supplierId === value ? undefined : supplierId);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedSupplier ? (
            <div className="flex flex-col items-start">
              <span className="font-medium">{selectedSupplier.name}</span>
              {selectedSupplier.email && (
                <span className="text-xs text-muted-foreground">
                  {selectedSupplier.email}
                </span>
              )}
            </div>
          ) : (
            placeholder
          )}
          {isLoading ? (
            <Loader2 className="ml-2 h-4 w-4 animate-spin" />
          ) : (
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput
            placeholder="Search suppliers..."
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandList>
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Loading suppliers...</span>
              </div>
            ) : (
              <>
                <CommandEmpty>
                  {searchTerm ? (
                    <div className="text-center p-4">
                      <p>No suppliers found matching "{searchTerm}"</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Try a different search term or add a new supplier
                      </p>
                    </div>
                  ) : (
                    <div className="text-center p-4">
                      <p>No suppliers found</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Add a new supplier to get started
                      </p>
                    </div>
                  )}
                </CommandEmpty>
                <CommandGroup>
                  {suppliers.map((supplier) => (
                    <CommandItem
                      key={supplier.id}
                      value={supplier.id.toString()}
                      onSelect={() => handleSelect(supplier.id)}
                      className="flex items-center justify-between p-3"
                    >
                      <div className="flex items-center">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value === supplier.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex flex-col">
                          <span className="font-medium">{supplier.name}</span>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            {supplier.phone && (
                              <span>📞 {supplier.phone}</span>
                            )}
                            {supplier.krapin ? (
                              <span className="text-green-600">
                                PIN: {supplier.krapin}
                              </span>
                            ) : (
                              <span className="text-orange-600">⚠️ No PIN</span>
                            )}
                          </div>
                          {supplier.email && (
                            <span className="text-xs text-muted-foreground">
                              {supplier.email}
                            </span>
                          )}
                        </div>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
