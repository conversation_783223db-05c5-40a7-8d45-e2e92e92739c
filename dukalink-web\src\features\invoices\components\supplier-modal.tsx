"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useCreateSupplier } from "@/features/suppliers/hooks/use-suppliers";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";

// Define the form schema for supplier creation
const supplierSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone number is required"),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  address: z.string().optional(),
  krapin: z.string().min(1, "KRA PIN is required"),
});

type SupplierFormValues = z.infer<typeof supplierSchema>;

interface SupplierModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSupplierCreated: (supplierId: number) => void;
}

export function SupplierModal({
  isOpen,
  onClose,
  onSupplierCreated,
}: SupplierModalProps) {
  const { data: currentUser } = useCurrentUser();
  const createSupplier = useCreateSupplier();

  // Initialize form with default values
  const form = useForm<SupplierFormValues>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      address: "",
      krapin: "",
    },
  });

  // Handle form submission
  const handleSubmit = async (data: SupplierFormValues) => {
    try {
      // Get tenant_id from current user
      const tenantId = currentUser?.tenant_id || 1;

      // Create supplier data
      const supplierData = {
        name: data.name,
        phone: data.phone,
        email: data.email || null,
        address: data.address || null,
        krapin: data.krapin,
        tenant_id: tenantId,
      };


      const response = await createSupplier.mutateAsync(supplierData as any);

      // Extract supplier ID from response
      const supplierId = response?.id || (response as any)?.data?.id;

      if (supplierId) {
        // Reset form
        form.reset();

        // Notify parent component
        onSupplierCreated(supplierId);
      } else {
        console.error("Supplier created but no ID returned:", response);
      }
    } catch (error) {
      console.error("Failed to create supplier:", error);
    }
  };

  // Handle modal close
  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add </DialogTitle>
          <DialogDescription>
            Create a new recipint for invoicing. All fields marked with * are required.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Name Field */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Name
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Enter supplier name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone Field */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Phone Number
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Enter phone number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email Field */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter email address"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* KRA PIN Field */}
            <FormField
              control={form.control}
              name="krapin"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    KRA PIN Number
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter KRA PIN number"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Required for KRA integration and tax compliance
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Address Field */}
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter supplier address"
                      {...field}
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={createSupplier.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createSupplier.isPending}
              >
                {createSupplier.isPending ? "Creating..." : "Create Supplier"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
