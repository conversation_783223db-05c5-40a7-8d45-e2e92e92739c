"use client";

import "@/components/ui/sidebar-collapsed.css";
import "@/components/ui/sidebar-compact.css";
import {
  Banknote,
  BarChart3 as BarChart3Icon,
  Building as BuildingIcon,
  CreditCard,
  FileText,
  GalleryVerticalEnd,
  Home as HomeIcon,
  Landmark,
  Package,
  Settings,
  ShoppingCart,
  Smartphone,
  UserCheck,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import { useSidebar } from "@/components/ui/sidebar";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";
// Import usePermissions for future use
// import { usePermissions } from "@/features/auth/hooks/use-permissions";
// import { getRoutePermission } from "@/lib/route-permissions";
// import { navigationPermissions } from "@/lib/navigation-permissions";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";

type NavItem = {
  title: string;
  url: string;
  icon?: any;
  isActive?: boolean;
  items?: { title: string; url: string }[];
};

/**
 * Maps route permission actions to grant actions
 * This function is commented out for MVP, will be used in the future
 * when the permission system is fully implemented
 *
 * @param action The action from route permissions
 * @returns The corresponding grant action
 */
// function mapActionToGrantAction(action: string): string {
//   switch (action) {
//     case "view":
//       return "read:any";
//     case "create":
//       return "create:any";
//     case "update":
//       return "update:any";
//     case "delete":
//       return "delete:any";
//     case "manage":
//       return "update:any"; // Manage maps to update:any
//     case "transfer":
//       return "update:any"; // Transfer maps to update:any
//     case "report":
//       return "read:any"; // Report maps to read:any
//     default:
//       return `${action}:any`;
//   }
// }

export function AppSidebar() {
  const { data: user } = useCurrentUser();
  const pathname = usePathname();
  // Permissions will be used in the future
  // const { hasPermission, permissions } = usePermissions();

  // Safely access role_name with type checking
  const userRoleName =
    user && typeof user === "object" && "role_name" in user
      ? user.role_name
      : "";

  // Get team name and plan based on user role
  const getTeamData = () => {
    if (user && typeof user === "object") {
      if (userRoleName === "super_admin") {
        return {
          name: "DukaLink",
          plan: "Enterprise",
        };
      } else if (
        userRoleName === "tenant_admin" ||
        userRoleName === "company_admin"
      ) {
        // For tenant/company admin, show tenant name and "Company" as plan
        return {
          name: user.tenant?.name || "Tenant",
          plan: "Company",
        };
      } else if (userRoleName === "branch_manager") {
        // For branch manager, show tenant name and branch name as plan
        return {
          name: user.tenant?.name || "Tenant",
          plan: user.branch?.name || "Branch",
        };
      }
    }
    return {
      name: "DukaLink",
      plan: "Enterprise",
    };
  };

  const teamData = getTeamData();

  // Define all navigation items
  const getAllNavigationItems = (): NavItem[] => [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: HomeIcon,
      isActive: pathname === "/dashboard" || pathname === "/",
    },
    {
      title: "Reports",
      url: "/reports/sales-summary",
      icon: BarChart3Icon,
      isActive:
        pathname.startsWith("/reports") ||
        pathname.startsWith("/expense-analytics"),
      items: [
        {
          title: "Sales Summary",
          url: "/reports/sales-summary",
        },
        {
          title: "Sales by Item",
          url: "/reports/sales-by-item",
        },
        {
          title: "Sales by Category",
          url: "/reports/sales-by-category",
        },
        {
          title: "Current Stock Levels",
          url: "/reports/current-stock-levels",
        },
        {
          title: "Stock History",
          url: "/reports/stock-history",
        },
        {
          title: "Banking Transactions",
          url: "/reports/banking-transactions",
        },
        {
          title: "Tax Report",
          url: "/reports/tax-report",
        },
        {
          title: "Banking Summary",
          url: "/banking?tab=summary",
        },
        {
          title: "M-Pesa Transactions",
          url: "/reports/mpesa-transactions",
        },
        {
          title: "Running Balances",
          url: "/reports/running-balances",
        },
        {
          title: "Cash Status",
          url: "/reports/cash-status",
        },
        {
          title: "Cash Float",
          url: "/reports/cash-float",
        },
        {
          title: "DSA Sales",
          url: "/reports/dsa-sales",
        },
        {
          title: "Receipts",
          url: "/reports/receipts",
        },
        {
          title: "Shifts",
          url: "/reports/shifts",
        },
        {
          title: "Phone Repairs",
          url: "/reports/phone-repairs",
        },
        {
          title: "Stock Valuation",
          url: "/inventory/valuation",
        },
        {
          title: "Stock Aging",
          url: "/reports/stock-aging",
        },
        {
          title: "Stock Movement",
          url: "/reports/stock-movement",
        },
        {
          title: "Expense Analytics",
          url: "/expense-analytics",
        },
        {
          title: "Customers",
          url: "/customers",
        },
      ],
    },
    {
      title: "Administration",
      url: "/users",
      icon: BuildingIcon,
      isActive:
        pathname.startsWith("/users") ||
        pathname.startsWith("/roles") ||
        pathname.startsWith("/rbac") ||
        pathname.startsWith("/branches") ||
        pathname.startsWith("/locations") ||
        pathname.startsWith("/employees"),
      items: [
        // Tenants menu item removed
        {
          title: "Users",
          url: "/users",
        },
        {
          title: "Roles",
          url: "/roles",
        },
        {
          title: "RBAC",
          url: "/rbac",
        },
        {
          title: "Branches",
          url: "/branches",
        },
        {
          title: "Locations",
          url: "/locations",
        },
        {
          title: "Employees",
          url: "/employees",
        },
      ],
    },
    {
      title: "Stock & Inventory",
      url: "/products",
      icon: Package,
      isActive:
        pathname.startsWith("/products") ||
        pathname.startsWith("/categories") ||
        pathname.startsWith("/brands") ||
        pathname.startsWith("/inventory") ||
        pathname.includes("/inventory/stock-cards") ||
        pathname.includes("/inventory/purchases"),
      items: [
        {
          title: "Products",
          url: "/products",
        },
        {
          title: "Categories",
          url: "/categories",
        },
        {
          title: "Brands",
          url: "/brands",
        },
        {
          title: "Inventory",
          url: "/inventory",
        },
        // Commented out Stock Locations as requested
        // {
        //   title: "Stock Locations",
        //   url: "/inventory/locations",
        // },
        {
          title: "Purchases",
          url: "/inventory/purchases",
        },
        {
          title: "Create Purchase",
          url: "/inventory/purchases/new",
        },
        {
          title: "Stock Requests",
          url: "/inventory/stock-requests",
        },
        {
          title: "Stock Transfers",
          url: "/inventory/transfers",
        },
        {
          title: "Make Stock Transfer",
          url: "/inventory/bulk-transfer",
        },
        {
          title: "Stock Transfer History",
          url: "/inventory/bulk-transfers",
        },
        {
          title: "Stock Cards",
          url: "/inventory/stock-cards",
        },
        {
          title: "Inventory Reports",
          url: "/inventory/reports",
        },
        {
          title: "Excel Import/Export",
          url: "/inventory/excel",
        },
      ],
    },
    {
      title: "Float Management",
      url: "/float",
      icon: Banknote,
      isActive: pathname.startsWith("/float"),
      items: [
        {
          title: "Float Dashboard",
          url: "/float",
        },
        {
          title: "Float Movements",
          url: "/float/movements",
        },
        {
          title: "Float Reconciliations",
          url: "/float/reconciliations",
        },
      ],
    },
    {
      title: "Banking Management",
      url: "/banking",
      icon: Landmark,
      isActive: pathname.startsWith("/banking"),
      items: [
        {
          title: "Banking Records",
          url: "/banking",
        },
      ],
    },
    {
      title: "Expenses Management",
      url: "/expenses",
      icon: Banknote,
      isActive: pathname.startsWith("/expenses"),
      items: [
        {
          title: "Expenses",
          url: "/expenses",
        },
      ],
    },
    {
      title: "Invoice Management",
      url: "/invoices",
      icon: FileText,
      isActive:
        pathname.startsWith("/invoices") ||
        pathname.startsWith("/credit-notes"),
      items: [
        {
          title: "All Invoices",
          url: "/invoices",
        },
        {
          title: "Credit Notes",
          url: "/credit-notes",
        },
      ],
    },
    {
      title: "DSA Management",
      url: "/dsa",
      icon: UserCheck,
      isActive: pathname.startsWith("/dsa"),
      items: [
        {
          title: "DSA Dashboard",
          url: "/dsa",
        },
        {
          title: "DSA Agents",
          url: "/dsa/customers",
        },
        {
          title: "Stock Assignments",
          url: "/dsa/assignments",
        },
        // Commented out DSA Reconciliations as requested
        // {
        //   title: "DSA Reconciliations",
        //   url: "/dsa/reconciliations",
        // },
      ],
    },
    {
      title: "Procurement",
      url: "/procurement",
      icon: ShoppingCart,
      isActive:
        pathname.startsWith("/procurement") ||
        pathname.startsWith("/suppliers"),
      items: [
        {
          title: "Procurement Dashboard",
          url: "/procurement",
        },
        {
          title: "Procurement Requests",
          url: "/procurement/requests",
        },
        {
          title: "Create Request",
          url: "/procurement/requests/new",
        },
        {
          title: "Procurement Receipts",
          url: "/procurement/receipts",
        },
        {
          title: "Suppliers",
          url: "/suppliers",
        },
      ],
    },
    {
      title: "Phone Repairs",
      url: "/phone-repairs",
      icon: Smartphone,
      isActive:
        pathname === "/phone-repairs" || pathname.startsWith("/phone-repairs/")
          ? true
          : false,
      items: [
        {
          title: "All Repairs",
          url: "/phone-repairs",
        },
        {
          title: "New Repair",
          url: "/phone-repairs/new",
        },
      ],
    },
    // Settings section completely hidden for production - not implemented
    // {
    //   title: "Settings",
    //   url: "/settings",
    //   icon: Settings,
    //   isActive:
    //     pathname.startsWith("/settings") || pathname.startsWith("/profile"),
    //   items: [
    //     {
    //       title: "System Settings",
    //       url: "/settings/system",
    //     },
    //     {
    //       title: "Company Settings",
    //       url: "/settings/company",
    //     },
    //     {
    //       title: "Payment Methods",
    //       url: "/settings/payment-methods",
    //     },
    //     {
    //       title: "System Health",
    //       url: "/settings/health",
    //     },
    //     {
    //       title: "Profile",
    //       url: "/profile",
    //     },
    //   ],
    // },
    // Permissions Demo section removed
  ];

  // Filter navigation items based on role (MVP approach)
  const getFilteredNavigationItemsByRole = (): NavItem[] => {
    const allItems = getAllNavigationItems();

    // Create a custom Employees section for branch managers
    const employeesSection: NavItem = {
      title: "Employees",
      url: "/employees",
      icon: UserCheck,
      isActive: pathname.startsWith("/employees"),
      items: [
        {
          title: "Manage Employees",
          url: "/employees",
        },
        {
          title: "Add Employee",
          url: "/employees/create",
        },
      ],
    };

    // For MVP, use role-based filtering as the default approach
    console.log(
      `[Navigation] ${userRoleName} role detected - using role-based filtering (MVP approach)`
    );

    // Filter navigation items based on user role
    const filteredItems = allItems.filter((item) => {
      // For super_admin and company_admin, show all items
      if (userRoleName === "super_admin" || userRoleName === "company_admin") {
        console.log(`[Navigation] Admin role detected - showing all items`);
        return true;
      }

      // For branch_admin, show most items
      if (userRoleName === "branch_admin") {
        // Hide Tenants section
        if (item.title === "Tenants") {
          return false;
        }
        return true;
      }

      // For accountant, show finance-related items
      if (userRoleName === "accountant") {
        // Allow Dashboard
        // Hide Tenants section
        if (item.title === "Dashboard") {
          return true;
        }

        if (item.title === "Tenants") {
          return false;
        }

        // Hide Administration routes for accountant
        if (item.title === "Administration") {
          return false;
        }

        // Allow Reports, Banking, Expenses, Float
        if (
          item.title === "Reports" ||
          item.title === "Banking Management" ||
          item.title === "Expenses Management" ||
          item.title === "Float Management"
        ) {
          return true;
        }

        // Hide DSA Management
        if (item.title === "DSA Management") {
          return false;
        }

        // Hide Phone Repairs
        if (item.title === "Phone Repairs") {
          return false;
        }

        // Allow POS Management for viewing sales data
        if (item.title === "POS Management") {
          return true;
        }

        // Hide Settings for accountant
        if (item.title === "Settings") {
          return false;
        }

        // Hide other sections
        return false;
      }

      // For auditor, show reporting and read-only sections
      if (userRoleName === "auditor") {
        // Allow Reports, Banking, Expenses, Float (read-only)
        if (
          item.title === "Dashboard" ||
          item.title === "Reports" ||
          item.title === "Banking Management" ||
          item.title === "Expenses Management" ||
          item.title === "Float Management" ||
          item.title === "POS Management" ||
          item.title === "Products & Inventory"
        ) {
          return true;
        }

        // Hide other sections
        return false;
      }

      // For finance_manager, show finance-related sections
      if (userRoleName === "finance_manager") {
        // Allow Reports, Banking, Expenses, Float
        if (
          item.title === "Dashboard" ||
          item.title === "Reports" ||
          item.title === "Banking Management" ||
          item.title === "Expenses Management" ||
          item.title === "Float Management" ||
          item.title === "POS Management"
        ) {
          return true;
        }

        // Hide other sections
        return false;
      }

      // For float_manager, show float-related sections
      if (userRoleName === "float_manager") {
        if (item.title === "Dashboard") {
          return true;
        }

        // Allow Float Management, Banking, POS, and Reports
        if (
          item.title === "Float Management" ||
          item.title === "Banking Management" ||
          item.title === "POS Management" ||
          item.title === "Reports"
        ) {
          return true;
        }

        // Hide other sections
        return false;
      }

      // For operations and operations_manager, show operations-related sections
      if (
        userRoleName === "operations" ||
        userRoleName === "operations_manager"
      ) {
        // Hide Administration routes
        if (item.title === "Administration") {
          return false;
        }

        // Hide Tenants section
        if (item.title === "Tenants") {
          return false;
        }

        // Allow everything else
        return true;
      }

      // For stock_admin, show inventory-related sections
      if (userRoleName === "stock_admin") {
        // Allow Products & Inventory
        if (
          item.title === "Products & Inventory" ||
          item.title === "Reports" ||
          item.title === "Dashboard" ||
          item.title === "Procurement"
        ) {
          return true;
        }

        // Hide other sections
        return false;
      }

      // Branch Manager role removed as they don't login via the web

      // For pos_operator and shop_attendant, hide admin sections
      if (
        userRoleName === "pos_operator" ||
        userRoleName === "shop_attendant"
      ) {
        // Hide Administration routes
        if (item.title === "Administration") {
          return false;
        }

        // Hide Float Management
        if (item.title === "Float Management") {
          return false;
        }

        // Hide Banking Management
        if (item.title === "Banking Management") {
          return false;
        }

        // Hide Expenses Management
        if (item.title === "Expenses Management") {
          return false;
        }

        // Hide DSA Management
        if (item.title === "DSA Management") {
          return false;
        }

        // Allow POS Management
        if (item.title === "POS Management") {
          return true;
        }

        // Allow Products & Inventory (read-only)
        if (item.title === "Products & Inventory") {
          return true;
        }

        // Hide other sections
        return false;
      }

      // For other roles, restrict access to only essential items
      console.log(
        `[Navigation] Unknown role detected: ${userRoleName} - restricting access`
      );

      // Allow Dashboard only
      if (item.title === "Dashboard") {
        return true;
      }

      // Hide Settings and everything else for unknown roles
      return false;
    });

    // Filter subitems based on role
    const itemsWithFilteredSubitems = filteredItems.map((item) => {
      // If the item has subitems, filter them based on role
      if (item.items && item.items.length > 0) {
        console.log(
          `[Navigation] Checking subitems for "${item.title}" section`
        );

        const filteredSubItems = item.items.filter((subItem) => {
          // For Administration, filter RBAC items for non-super_admin users
          if (item.title === "Administration") {
            // Only super_admin can see RBAC items
            if (subItem.title === "RBAC") {
              return userRoleName === "super_admin";
            }
            // All other admin roles can see other items
            return true;
          }

          // For Settings, only show Profile for most roles
          if (item.title === "Settings") {
            // Admin roles can see all settings
            if (
              userRoleName === "super_admin" ||
              userRoleName === "company_admin" ||
              userRoleName === "branch_admin"
            ) {
              return true;
            }

            // Accountant can see Profile and Payment Methods
            if (userRoleName === "accountant") {
              return (
                subItem.title === "Profile" ||
                subItem.title === "Payment Methods"
              );
            }

            // Other roles can only see Profile
            return subItem.title === "Profile";
          }

          // For POS Management, restrict Cash Balance to admin roles and float_manager
          if (
            item.title === "POS Management" &&
            subItem.title === "Cash Balance"
          ) {
            return (
              userRoleName === "super_admin" ||
              userRoleName === "company_admin" ||
              userRoleName === "branch_admin" ||
              userRoleName === "accountant" ||
              userRoleName === "finance_manager" ||
              userRoleName === "float_manager"
            );
          }

          // For Products & Inventory, restrict Categories and Brands for non-admin roles
          if (
            item.title === "Products & Inventory" &&
            (subItem.title.includes("Categories") ||
              subItem.title.includes("Brands"))
          ) {
            return (
              userRoleName === "super_admin" ||
              userRoleName === "company_admin" ||
              userRoleName === "branch_admin" ||
              userRoleName === "stock_admin"
            );
          }

          // For Products & Inventory, restrict inventory management for shop_attendant and pos_operator
          if (
            item.title === "Products & Inventory" &&
            (subItem.title.includes("Transfer") ||
              subItem.title.includes("Stock Cards") ||
              subItem.title.includes("Inventory Reports") ||
              subItem.title.includes("Excel"))
          ) {
            return (
              userRoleName !== "pos_operator" &&
              userRoleName !== "shop_attendant"
            );
          }

          // For Reports, restrict certain reports based on role
          if (item.title === "Reports") {
            // Finance-related reports
            if (
              subItem.title.includes("Banking") ||
              subItem.title.includes("Cash") ||
              subItem.title.includes("Float") ||
              subItem.title.includes("Expense")
            ) {
              return (
                userRoleName === "super_admin" ||
                userRoleName === "company_admin" ||
                userRoleName === "branch_admin" ||
                userRoleName === "accountant" ||
                userRoleName === "finance_manager" ||
                userRoleName === "auditor"
              );
            }

            // Inventory-related reports
            if (
              subItem.title.includes("Stock") ||
              subItem.title.includes("Inventory")
            ) {
              return (
                userRoleName === "super_admin" ||
                userRoleName === "company_admin" ||
                userRoleName === "branch_admin" ||
                userRoleName === "stock_admin" ||
                userRoleName === "operations_manager" ||
                userRoleName === "operations" ||
                userRoleName === "auditor"
              );
            }
          }

          // For Procurement, restrict certain operations based on role
          if (item.title === "Procurement") {
            // Creating and approving procurement requests
            if (subItem.title.includes("Create")) {
              return (
                userRoleName === "super_admin" ||
                userRoleName === "company_admin" ||
                userRoleName === "branch_admin" ||
                userRoleName === "stock_admin" ||
                userRoleName === "operations_manager"
              );
            }
          }

          // For Float Management, restrict certain operations based on role
          if (item.title === "Float Management") {
            // Float reconciliations
            if (subItem.title.includes("Reconciliation")) {
              return (
                userRoleName === "super_admin" ||
                userRoleName === "company_admin" ||
                userRoleName === "accountant" ||
                userRoleName === "finance_manager" ||
                userRoleName === "float_manager"
              );
            }
          }

          // Allow all other subitems by default
          return true;
        });

        // Update the item's subitems with the filtered list
        item.items = filteredSubItems;
      }

      return item;
    });

    // Only return items that have at least one subitem (if they had subitems to begin with)
    const result = itemsWithFilteredSubitems.filter(
      (item) => !item.items || item.items.length > 0
    );

    // Add the Employees section at the beginning of the filtered items for branch managers
    if (userRoleName === "branch_manager") {
      return [employeesSection, ...result];
    }

    return result;
  };

  // Filter navigation items based on permissions (for future use)
  // This function is commented out for MVP, will be implemented in the future
  // when the permission system is fully implemented

  // Sample data for the sidebar
  const data = {
    user: {
      name: user?.name || "User",
      email: user?.email || "<EMAIL>",
      avatar: "/placeholder-avatar.jpg",
    },
    teams: [
      {
        name: teamData.name,
        logo: GalleryVerticalEnd,
        plan: teamData.plan,
      },
    ],
    // For MVP, use role-based filtering
    navMain: getFilteredNavigationItemsByRole(),
    // For future: navMain: getFilteredNavigationItems(),
  };

  const { state, isMobile } = useSidebar();
  const isCollapsed = state === "collapsed" && !isMobile;

  return (
    <div
      className={cn(
        "h-full flex flex-col sidebar-compact",
        isCollapsed && "sidebar-collapsed"
      )}
    >
      <div className="flex-1 overflow-y-auto py-2">
        <div className={cn("px-3", isCollapsed && "px-2")}>
          <NavMain items={data.navMain} />
        </div>
      </div>
      <div className={cn("p-3 border-t", isCollapsed && "p-2")}>
        <NavUser user={data.user} />
      </div>
    </div>
  );
}
