const { Supplier, Tenant } = require('../models');
const AppError = require('../utils/error');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

/**
 * Get all suppliers with filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllSuppliers = async (req, res, next) => {
  try {
    const {
      search,
      page = 1,
      limit = 10
    } = req.query;

    // Ensure limit and page are numbers
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 10;
    const offset = (pageNum - 1) * limitNum;

    // Build where clause based on filters
    const whereClause = {};

    // Search by name, contact person, phone, or email
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { contact_person: { [Op.like]: `%${search}%` } },
        { phone: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }

    // For non-super_admin users, restrict to their tenant
    if (req.user.role_name !== 'super_admin' && req.user.tenant_id) {
      whereClause.tenant_id = req.user.tenant_id;
    }

    // Get suppliers with pagination
    const { count, rows: suppliers } = await Supplier.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Tenant,
          as: 'Tenant',
          attributes: ['id', 'name']
        }
      ],
      order: [['name', 'ASC']],
      limit: limitNum,
      offset: offset
    });

    return res.status(200).json({
      data: suppliers,
      pagination: {
        total: count,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(count / limitNum)
      }
    });
  } catch (error) {
    logger.error(`Error getting suppliers: ${error.message}`);
    next(error);
  }
};

/**
 * Get supplier by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSupplierById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const supplier = await Supplier.findByPk(id, {
      include: [
        {
          model: Tenant,
          as: 'Tenant',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }

    // Check if user has permission to view this supplier
    if (req.user.role_name !== 'super_admin') {
      if (supplier.tenant_id !== req.user.tenant_id) {
        return next(new AppError('You do not have permission to view this supplier', 403));
      }
    }

    return res.status(200).json(supplier);
  } catch (error) {
    logger.error(`Error getting supplier by ID: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new supplier
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createSupplier = async (req, res, next) => {
  try {
    const {
      name,
      krapin,
      contact_person,
      phone,
      email,
      address,
      po_box
    } = req.body;

    // Validate required fields
    if (!name) {
      return next(new AppError('Supplier name is required', 400));
    }

    if (!krapin) {
      return next(new AppError('KRA PIN is required', 400));
    }

    // Get tenant_id from user
    const tenant_id = req.user.tenant_id;

    // Create supplier
    const supplier = await Supplier.create({
      name,
      krapin,
      contact_person,
      phone,
      email,
      address,
      po_box,
      tenant_id,
      created_by: req.user.id
    });

    return res.status(201).json(supplier);
  } catch (error) {
    logger.error(`Error creating supplier: ${error.message}`);
    next(error);
  }
};

/**
 * Update a supplier
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateSupplier = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      name,
      krapin,
      contact_person,
      phone,
      email,
      address,
      po_box
    } = req.body;

    // Find the supplier
    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }

    // Check if user has permission to update this supplier
    if (req.user.role_name !== 'super_admin') {
      if (supplier.tenant_id !== req.user.tenant_id) {
        return next(new AppError('You do not have permission to update this supplier', 403));
      }
    }

    // Update supplier
    await supplier.update({
      name: name || supplier.name,
      krapin: krapin || supplier.krapin,
      contact_person: contact_person !== undefined ? contact_person : supplier.contact_person,
      phone: phone !== undefined ? phone : supplier.phone,
      email: email !== undefined ? email : supplier.email,
      address: address !== undefined ? address : supplier.address,
      po_box: po_box !== undefined ? po_box : supplier.po_box
    });

    return res.status(200).json(supplier);
  } catch (error) {
    logger.error(`Error updating supplier: ${error.message}`);
    next(error);
  }
};

/**
 * Delete a supplier
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteSupplier = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find the supplier
    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }

    // Check if user has permission to delete this supplier
    if (req.user.role_name !== 'super_admin') {
      if (supplier.tenant_id !== req.user.tenant_id) {
        return next(new AppError('You do not have permission to delete this supplier', 403));
      }
    }

    // Check if supplier has associated purchases
    const purchaseCount = await supplier.countPurchases();

    if (purchaseCount > 0) {
      return next(new AppError('Cannot delete supplier with associated purchases', 400));
    }

    // Delete supplier
    await supplier.destroy();

    return res.status(200).json({ message: 'Supplier deleted successfully' });
  } catch (error) {
    logger.error(`Error deleting supplier: ${error.message}`);
    next(error);
  }
};

module.exports = {
  getAllSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier
};
