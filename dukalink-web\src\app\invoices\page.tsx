"use client";

import React, { useState } from "react";
import { MainLayout } from "@/components/layouts/main-layout";
import { InvoiceTable } from "@/features/invoices/components/invoice-table";
import { useInvoices } from "@/features/invoices/hooks/use-invoices";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { InvoiceFilters, InvoiceStatus } from "@/features/invoices/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { Button } from "@/components/ui/button";
import { format, subMonths } from "date-fns";
import { useSuppliers } from "@/features/inventory/hooks/use-suppliers";
import { Filter, RefreshCw, Plus } from "lucide-react";
import { useRouter } from "next/navigation";

export default function InvoicesPage() {
  const router = useRouter();

  // Set default filter dates (last month to today)
  const defaultStartDate = subMonths(new Date(), 1);
  const defaultEndDate = new Date();

  // State for supplier invoice filters
  const [invoiceFilters, setInvoiceFilters] = useState<InvoiceFilters>({
    page: 1,
    limit: 50, // Changed default to 50
    type: 'supplier', // Only supplier invoices
    sort_by: 'created_at',
    sort_direction: 'desc',
  });

  // State for filter form
  const [filterForm, setFilterForm] = useState({
    status: "all" as InvoiceStatus | "all",
    supplier_id: "all",
    start_date: defaultStartDate,
    end_date: defaultEndDate,
  });

  // State for showing filter panel
  const [showFilters, setShowFilters] = useState(false);

  // Fetch invoice data
  const { data: invoiceData, isLoading: invoicesLoading, refetch: refetchInvoices, error: invoicesError } = useInvoices(invoiceFilters);

  const invoices = invoiceData?.data || [];
  const invoicePagination = invoiceData?.pagination || { page: 1, totalPages: 1, totalItems: 0 };

  // Log any errors for debugging
  React.useEffect(() => {
    if (invoicesError) {
      console.error("Error fetching invoices:", invoicesError);
    }
  }, [invoicesError]);

  // Fetch suppliers for filters
  const { data: suppliersData } = useSuppliers();
  const suppliers = suppliersData?.data || [];

  // Handle page change
  const handlePageChange = (page: number) => {
    setInvoiceFilters((prev) => ({ ...prev, page }));
  };

  // Handle search
  const handleSearch = (query: string) => {
    setInvoiceFilters((prev) => ({ ...prev, search: query, page: 1 }));
  };

  // Handle sorting
  const handleSort = (field: string, direction: 'asc' | 'desc') => {
    setInvoiceFilters((prev) => ({
      ...prev,
      sort_by: field,
      sort_direction: direction,
      page: 1
    }));
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setInvoiceFilters((prev) => ({ ...prev, limit: value, page: 1 }));
  };

  // Apply filters
  const applyFilters = () => {
    const newInvoiceFilters: InvoiceFilters = {
      ...invoiceFilters,
      page: 1,
      status: filterForm.status === "all" ? undefined : filterForm.status,
      type: 'supplier', // Always supplier
      supplier_id: filterForm.supplier_id === "all" ? undefined : parseInt(filterForm.supplier_id),
      start_date: filterForm.start_date ? format(filterForm.start_date, "yyyy-MM-dd") : undefined,
      end_date: filterForm.end_date ? format(filterForm.end_date, "yyyy-MM-dd") : undefined,
    };
    setInvoiceFilters(newInvoiceFilters);
  };

  // Reset filters
  const resetFilters = () => {
    setFilterForm({
      status: "all" as InvoiceStatus | "all",
      supplier_id: "all",
      start_date: defaultStartDate,
      end_date: defaultEndDate,
    });

    setInvoiceFilters({
      page: 1,
      limit: 50,
      type: 'supplier',
      sort_by: 'created_at',
      sort_direction: 'desc',
    });
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Invoices</h1>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="mr-2 h-4 w-4" />
              {showFilters ? "Hide Filters" : "Show Filters"}
            </Button>
            <Button variant="outline" onClick={() => refetchInvoices()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button onClick={() => router.push("/invoices/new")}>
              <Plus className="mr-2 h-4 w-4" />
              Create Invoice
            </Button>
          </div>
        </div>

        {/* Error message */}
        {invoicesError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
            <div className="flex items-center">
              <div className="py-1">
                <svg className="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                </svg>
              </div>
              <div>
                <p className="font-bold">Error loading invoices</p>
                <p className="text-sm">There was a problem connecting to the server. Please try again later or contact support if the problem persists.</p>
              </div>
            </div>
            <div className="mt-3">
              <Button variant="outline" size="sm" onClick={() => refetchInvoices()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </div>
          </div>
        )}

        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Status Filter */}
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={filterForm.status}
                    onValueChange={(value) =>
                      setFilterForm((prev) => ({ ...prev, status: value as InvoiceStatus | "all" }))
                    }
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="sent">Sent</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="partially_paid">Partially Paid</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="reconciled">Reconciled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Supplier Filter */}
                <div className="space-y-2">
                  <Label htmlFor="supplier">Supplier</Label>
                  <Select
                    value={filterForm.supplier_id}
                    onValueChange={(value) =>
                      setFilterForm((prev) => ({ ...prev, supplier_id: value }))
                    }
                  >
                    <SelectTrigger id="supplier">
                      <SelectValue placeholder="All Suppliers" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Suppliers</SelectItem>
                      {suppliers.map((supplier) => (
                        <SelectItem
                          key={supplier.id}
                          value={supplier.id.toString()}
                        >
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Date Range */}
                <div className="space-y-2">
                  <Label>Start Date</Label>
                  <DatePicker
                    date={filterForm.start_date}
                    setDate={(date) => {
                      if (date !== filterForm.start_date) {
                        setFilterForm((prev) => ({ ...prev, start_date: date || defaultStartDate }));
                      }
                    }}
                    id="start-date"
                  />
                </div>

                <div className="space-y-2">
                  <Label>End Date</Label>
                  <DatePicker
                    date={filterForm.end_date}
                    setDate={(date) => {
                      if (date !== filterForm.end_date) {
                        setFilterForm((prev) => ({ ...prev, end_date: date || defaultEndDate }));
                      }
                    }}
                    id="end-date"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={resetFilters}>
                  Reset
                </Button>
                <Button onClick={applyFilters}>Apply Filters</Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Invoice Table */}
        <InvoiceTable
          invoices={invoices}
          isLoading={invoicesLoading}
          pagination={invoicePagination}
          onPageChange={handlePageChange}
          onSearch={handleSearch}
          onSort={handleSort}
          sortField={invoiceFilters.sort_by}
          sortDirection={invoiceFilters.sort_direction}
          onItemsPerPageChange={handleItemsPerPageChange}
          activeFilters={{
            status: filterForm.status === "all" ? undefined : filterForm.status,
            type: 'supplier',
            supplier_id: filterForm.supplier_id === "all" ? undefined : parseInt(filterForm.supplier_id),
            date_range: filterForm.start_date && filterForm.end_date ? {
              start: format(filterForm.start_date, "yyyy-MM-dd"),
              end: format(filterForm.end_date, "yyyy-MM-dd")
            } : undefined
          }}
          onClearFilters={resetFilters}
        />
      </div>
    </MainLayout>
  );
}
